const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let tableCreated = false;

// إنشاء جدول العمال المثاليين إذا لم يكن موجوداً
const createIdealEmployeesTable = async (pool) => {
  if (tableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS ideal_employees (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        from_period date NOT NULL,
        to_period date NOT NULL,
        evaluation_score decimal(5,2) NOT NULL,
        reward_amount decimal(10,2) NOT NULL,
        selection_reason text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_department (department),
        KEY idx_period (from_period,to_period),
        KEY idx_evaluation_score (evaluation_score),
        KEY idx_created_at (created_at)
      ) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العامل المثالي'
    `);
    tableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول العمال المثاليين:', error);
    throw error;
  }
};

// اختبار API للعامل المثالي
router.get('/test', (req, res) => {
  res.json({ message: 'API العامل المثالي يعمل بنجاح', timestamp: new Date().toISOString() });
});

// الحصول على جميع العمال المثاليين
router.get('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const [rows] = await pool.promise().execute(
      'SELECT * FROM ideal_employees ORDER BY from_period DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين:', error);
    res.status(500).json({ message: 'خطأ في جلب البيانات' });
  }
});

// إضافة عامل مثالي جديد
router.post('/', authenticateToken, async (req, res) => {
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;

  console.log('تم استقبال طلب POST /api/ideal-employees مع البيانات:', req.body);

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !from_period || !to_period || !evaluation_score || !reward_amount || !selection_reason) {
      return res.status(400).json({ message: 'جميع الحقول مطلوبة' });
    }

    // التحقق من وجود الموظف في جدول الموظفين
    const [employeeCheck] = await pool.promise().execute(
      "SELECT code, full_name, department, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ message: 'الموظف غير موجود في النظام' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ message: 'لا يمكن إضافة عامل مثالي لموظف مستقيل' });
    }

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees 
       WHERE employee_code = ? 
       AND ((from_period <= ? AND to_period >= ?) 
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [employee_code, from_period, from_period, to_period, to_period, from_period, to_period]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({ 
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.' 
      });
    }

    const [result] = await pool.promise().execute(
      `INSERT INTO ideal_employees 
       (employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes]
    );

    console.log('تم إدراج العامل المثالي بنجاح، ID:', result.insertId);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'ideal_employees',
      record_id: result.insertId.toString(),
      message: `تم إضافة عامل مثالي: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الفترة: من ${from_period} إلى ${to_period} - الدرجة: ${evaluation_score} - المكافأة: ${reward_amount}`
    });

    res.status(201).json({ 
      message: 'تم إضافة العامل المثالي بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في إضافة البيانات' });
  }
});

// تحديث عامل مثالي
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    employee_code,
    employee_name,
    department,
    from_period,
    to_period,
    evaluation_score,
    reward_amount,
    selection_reason,
    notes
  } = req.body;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // التحقق من البيانات المطلوبة
    if (!employee_code || !employee_name || !department || !from_period || !to_period || !evaluation_score || !reward_amount || !selection_reason) {
      return res.status(400).json({ message: 'جميع الحقول مطلوبة' });
    }

    // التحقق من وجود الموظف في جدول الموظفين
    const [employeeCheck] = await pool.promise().execute(
      "SELECT code, full_name, department, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ message: 'الموظف غير موجود في النظام' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ message: 'لا يمكن تحديث عامل مثالي لموظف مستقيل' });
    }

    // الحصول على البيانات القديمة للمقارنة
    const [oldDataResult] = await pool.promise().execute(
      'SELECT * FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const oldData = oldDataResult[0];

    // التحقق من عدم وجود تداخل في الفترات لنفس الموظف (باستثناء السجل الحالي)
    const [existingRecords] = await pool.promise().execute(
      `SELECT id FROM ideal_employees 
       WHERE employee_code = ? AND id != ?
       AND ((from_period <= ? AND to_period >= ?) 
            OR (from_period <= ? AND to_period >= ?)
            OR (from_period >= ? AND to_period <= ?))`,
      [employee_code, id, from_period, from_period, to_period, to_period, from_period, to_period]
    );

    if (existingRecords.length > 0) {
      return res.status(400).json({ 
        message: 'يوجد تداخل في الفترات الزمنية لهذا الموظف. يرجى اختيار فترة مختلفة.' 
      });
    }

    const [result] = await pool.promise().execute(
      `UPDATE ideal_employees SET 
       employee_code = ?, employee_name = ?, department = ?, from_period = ?, to_period = ?, 
       evaluation_score = ?, reward_amount = ?, selection_reason = ?, notes = ?
       WHERE id = ?`,
      [employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount, selection_reason, notes, id]
    );

    // إنشاء رسالة التغييرات التفصيلية
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      from_period: 'من تاريخ',
      to_period: 'إلى تاريخ',
      evaluation_score: 'درجة التقييم',
      reward_amount: 'مبلغ المكافأة',
      selection_reason: 'سبب الاختيار',
      notes: 'الملاحظات'
    };

    const newData = { from_period, to_period, evaluation_score, reward_amount, selection_reason, notes };
    const editMessage = createEditMessage('عامل مثالي', oldData, newData, fieldLabels);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم تعديل عامل مثالي: ${oldData.employee_name} (كود: ${oldData.employee_code}) - ${editMessage}`
    });

    res.json({ message: 'تم تحديث العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في تحديث البيانات' });
  }
});

// حذف عامل مثالي
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    // الحصول على بيانات العامل المثالي قبل الحذف
    const [idealEmployeeData] = await pool.promise().execute(
      'SELECT employee_code, employee_name, department, from_period, to_period, evaluation_score, reward_amount FROM ideal_employees WHERE id = ?',
      [id]
    );

    if (idealEmployeeData.length === 0) {
      return res.status(404).json({ message: 'العامل المثالي غير موجود' });
    }

    const idealEmployee = idealEmployeeData[0];

    const [result] = await pool.promise().execute(
      'DELETE FROM ideal_employees WHERE id = ?',
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'ideal_employees',
      record_id: id.toString(),
      message: `تم حذف عامل مثالي: ${idealEmployee.employee_name} (كود: ${idealEmployee.employee_code}) - القسم: ${idealEmployee.department} - الفترة: من ${idealEmployee.from_period} إلى ${idealEmployee.to_period} - الدرجة: ${idealEmployee.evaluation_score} - المكافأة: ${idealEmployee.reward_amount}`
    });

    res.json({ message: 'تم حذف العامل المثالي بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العامل المثالي:', error);
    res.status(500).json({ message: 'خطأ في حذف البيانات' });
  }
});

// البحث في العمال المثاليين
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const { employee_code, employee_name, department, start_date, end_date, min_score, max_score } = req.query;

    let query = "SELECT * FROM ideal_employees WHERE 1=1";
    const params = [];

    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }

    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }

    if (department) {
      query += " AND department = ?";
      params.push(department);
    }

    if (start_date) {
      query += " AND from_period >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND to_period <= ?";
      params.push(end_date);
    }

    if (min_score) {
      query += " AND evaluation_score >= ?";
      params.push(parseFloat(min_score));
    }

    if (max_score) {
      query += " AND evaluation_score <= ?";
      params.push(parseFloat(max_score));
    }

    query += " ORDER BY from_period DESC, evaluation_score DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن العمال المثاليين:', error);
    res.status(500).json({ error: 'فشل في البحث عن العمال المثاليين' });
  }
});

// إحصائيات العمال المثاليين
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createIdealEmployeesTable(pool);

    const { department, start_date, end_date } = req.query;

    let query = `SELECT
      COUNT(*) as total_count,
      AVG(evaluation_score) as avg_score,
      MAX(evaluation_score) as max_score,
      MIN(evaluation_score) as min_score,
      SUM(reward_amount) as total_rewards,
      AVG(reward_amount) as avg_reward
      FROM ideal_employees WHERE 1=1`;
    const params = [];

    if (department) {
      query += " AND department = ?";
      params.push(department);
    }

    if (start_date) {
      query += " AND from_period >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND to_period <= ?";
      params.push(end_date);
    }

    const [stats] = await pool.promise().query(query, params);

    // إحصائيات حسب القسم
    let departmentQuery = `SELECT
      department,
      COUNT(*) as count,
      AVG(evaluation_score) as avg_score,
      SUM(reward_amount) as total_rewards
      FROM ideal_employees WHERE 1=1`;
    const departmentParams = [];

    if (start_date) {
      departmentQuery += " AND from_period >= ?";
      departmentParams.push(start_date);
    }

    if (end_date) {
      departmentQuery += " AND to_period <= ?";
      departmentParams.push(end_date);
    }

    departmentQuery += " GROUP BY department ORDER BY count DESC";

    const [departmentStats] = await pool.promise().query(departmentQuery, departmentParams);

    res.json({
      overall: stats[0],
      by_department: departmentStats
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات العمال المثاليين:', error);
    res.status(500).json({ error: 'فشل في جلب الإحصائيات' });
  }
});

// الحصول على العمال المثاليين حسب الموظف
router.get('/employee/:code', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { code } = req.params;

    const [rows] = await pool.promise().query(`
      SELECT * FROM ideal_employees
      WHERE employee_code = ?
      ORDER BY from_period DESC
    `, [code]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين للموظف:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الموظف' });
  }
});

// الحصول على العمال المثاليين حسب القسم
router.get('/department/:department', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { department } = req.params;

    const [rows] = await pool.promise().query(`
      SELECT * FROM ideal_employees
      WHERE department = ?
      ORDER BY from_period DESC, evaluation_score DESC
    `, [department]);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب العمال المثاليين للقسم:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات القسم' });
  }
});

module.exports = router;
